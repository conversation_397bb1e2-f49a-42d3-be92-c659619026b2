-- Credit Card Phase 3 Data Migration
-- This script migrates existing data to the new schema and sets default values for new columns

-- Wrap everything in a transaction to ensure atomicity
BEGIN;

-- 1. Update existing credit cards with default values for new columns
DO $$
BEGIN
    -- Check if the new columns exist
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'credit_cards' 
        AND column_name = 'rewards_rate'
    ) THEN
        -- Set default values for new columns
        UPDATE credit_cards
        SET 
            rewards_rate = CASE 
                WHEN rewards_program IS NOT NULL AND rewards_program != '' 
                THEN 1.0 -- Default 1% rewards rate if they have a rewards program
                ELSE NULL 
            END,
            foreign_transaction_fee = 3.0, -- Default 3% foreign transaction fee
            late_payment_fee = 500, -- Default $500 late payment fee
            min_payment_percentage = 5.0, -- Default 5% minimum payment
            grace_period = 21, -- Default 21 days grace period
            benefits = '{}' -- Empty JSON object for benefits
        WHERE 
            rewards_rate IS NULL OR
            foreign_transaction_fee IS NULL OR
            late_payment_fee IS NULL OR
            min_payment_percentage IS NULL OR
            grace_period IS NULL OR
            benefits IS NULL;
            
        RAISE NOTICE 'Updated existing credit cards with default values for new columns';
    ELSE
        RAISE NOTICE 'New columns do not exist in credit_cards table. Skipping update.';
    END IF;
END $$;

-- 2. Create initial credit scores based on user data (if available)
DO $$
BEGIN
    -- Check if the credit_scores table exists
    IF EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_name = 'credit_scores'
    ) THEN
        -- Insert a default credit score for each user who has a credit card
        -- but doesn't have any credit scores yet
        INSERT INTO credit_scores (
            user_id, 
            score, 
            source, 
            date, 
            notes
        )
        SELECT DISTINCT 
            cc.user_id, 
            700, -- Default "good" credit score
            'System Generated', 
            CURRENT_DATE, 
            'Initial credit score generated during migration'
        FROM 
            credit_cards cc
        WHERE 
            NOT EXISTS (
                SELECT 1 
                FROM credit_scores cs 
                WHERE cs.user_id = cc.user_id
            );
            
        RAISE NOTICE 'Created initial credit scores for users without existing scores';
    ELSE
        RAISE NOTICE 'credit_scores table does not exist. Skipping credit score creation.';
    END IF;
END $$;

-- 3. Generate sample rewards data based on existing transactions
DO $$
DECLARE
    transaction_count INTEGER;
    rewards_count INTEGER;
BEGIN
    -- Check if the credit_card_rewards table exists
    IF EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_name = 'credit_card_rewards'
    ) THEN
        -- Count existing transactions that might qualify for rewards
        SELECT COUNT(*) INTO transaction_count
        FROM transactions t
        JOIN credit_cards cc ON t.metadata->>'creditCardId' = cc.id
        WHERE 
            t.type = 'expense' AND
            t.amount > 0 AND
            cc.rewards_program IS NOT NULL AND
            cc.rewards_program != '';
            
        -- Count existing rewards
        SELECT COUNT(*) INTO rewards_count
        FROM credit_card_rewards;
        
        -- Only generate sample rewards if we have qualifying transactions
        -- and no existing rewards
        IF transaction_count > 0 AND rewards_count = 0 THEN
            -- Insert sample rewards based on transactions
            INSERT INTO credit_card_rewards (
                user_id,
                credit_card_id,
                reward_type,
                amount,
                description,
                date,
                category,
                is_redeemed,
                redeemed_date,
                redeemed_value
            )
            SELECT 
                t.user_id,
                cc.id,
                CASE 
                    WHEN cc.rewards_program ILIKE '%cash%' THEN 'cashback'
                    WHEN cc.rewards_program ILIKE '%point%' THEN 'points'
                    WHEN cc.rewards_program ILIKE '%mile%' THEN 'miles'
                    ELSE 'cashback'
                END,
                CASE 
                    WHEN cc.rewards_program ILIKE '%cash%' THEN t.amount * 0.01 -- 1% cashback
                    WHEN cc.rewards_program ILIKE '%point%' THEN t.amount * 1 -- 1 point per unit
                    WHEN cc.rewards_program ILIKE '%mile%' THEN t.amount * 0.5 -- 0.5 miles per unit
                    ELSE t.amount * 0.01 -- Default 1% cashback
                END,
                'Reward for transaction on ' || t.date,
                t.date,
                t.category,
                false, -- Not redeemed
                NULL,
                NULL
            FROM 
                transactions t
            JOIN 
                credit_cards cc ON t.metadata->>'creditCardId' = cc.id
            WHERE 
                t.type = 'expense' AND
                t.amount > 0 AND
                cc.rewards_program IS NOT NULL AND
                cc.rewards_program != ''
            LIMIT 100; -- Limit to 100 sample rewards
            
            RAISE NOTICE 'Generated sample rewards based on existing transactions';
        ELSE
            RAISE NOTICE 'No qualifying transactions found or rewards already exist. Skipping reward generation.';
        END IF;
    ELSE
        RAISE NOTICE 'credit_card_rewards table does not exist. Skipping reward generation.';
    END IF;
END $$;

-- 4. Populate credit card features based on existing credit cards
DO $$
BEGIN
    -- Check if the credit_card_features table exists
    IF EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_name = 'credit_card_features'
    ) AND EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_name = 'credit_card_comparison_features'
    ) THEN
        -- First, create some standard comparison features if none exist
        INSERT INTO credit_card_comparison_features (
            id,
            user_id,
            name,
            description,
            importance
        )
        SELECT 
            gen_random_uuid(),
            user_id,
            feature_name,
            feature_description,
            importance
        FROM (
            SELECT DISTINCT 
                cc.user_id,
                unnest(ARRAY[
                    'Annual Fee', 
                    'Rewards Rate', 
                    'Foreign Transaction Fee',
                    'Interest Rate',
                    'Credit Limit',
                    'Travel Benefits',
                    'Cash Back',
                    'Sign-up Bonus'
                ]) as feature_name,
                unnest(ARRAY[
                    'Annual fee charged for the card', 
                    'Rate at which rewards are earned',
                    'Fee charged on foreign transactions',
                    'Interest rate on unpaid balances',
                    'Maximum credit limit offered',
                    'Travel-related benefits like lounge access',
                    'Cash back percentage on purchases',
                    'Bonus offered for new cardholders'
                ]) as feature_description,
                unnest(ARRAY[
                    'high', 
                    'high',
                    'medium',
                    'high',
                    'medium',
                    'low',
                    'high',
                    'medium'
                ]) as importance
            FROM credit_cards cc
        ) as features
        WHERE NOT EXISTS (
            SELECT 1 
            FROM credit_card_comparison_features ccf 
            WHERE ccf.user_id = features.user_id AND ccf.name = features.feature_name
        );
        
        -- Now, populate some basic features for existing cards
        INSERT INTO credit_card_features (
            id,
            credit_card_id,
            feature_id,
            value
        )
        SELECT 
            gen_random_uuid(),
            cc.id,
            ccf.id,
            CASE 
                WHEN ccf.name = 'Annual Fee' THEN cc.annual_fee::text
                WHEN ccf.name = 'Rewards Rate' THEN cc.rewards_rate::text
                WHEN ccf.name = 'Foreign Transaction Fee' THEN cc.foreign_transaction_fee::text
                WHEN ccf.name = 'Interest Rate' THEN cc.interest_rate::text
                WHEN ccf.name = 'Credit Limit' THEN cc.credit_limit::text
                ELSE NULL
            END
        FROM 
            credit_cards cc
        CROSS JOIN 
            credit_card_comparison_features ccf
        WHERE 
            cc.user_id = ccf.user_id AND
            NOT EXISTS (
                SELECT 1 
                FROM credit_card_features ccft 
                WHERE ccft.credit_card_id = cc.id AND ccft.feature_id = ccf.id
            ) AND
            CASE 
                WHEN ccf.name = 'Annual Fee' THEN cc.annual_fee::text
                WHEN ccf.name = 'Rewards Rate' THEN cc.rewards_rate::text
                WHEN ccf.name = 'Foreign Transaction Fee' THEN cc.foreign_transaction_fee::text
                WHEN ccf.name = 'Interest Rate' THEN cc.interest_rate::text
                WHEN ccf.name = 'Credit Limit' THEN cc.credit_limit::text
                ELSE NULL
            END IS NOT NULL;
            
        RAISE NOTICE 'Populated credit card features based on existing credit cards';
    ELSE
        RAISE NOTICE 'Credit card feature tables do not exist. Skipping feature population.';
    END IF;
END $$;

-- Commit the transaction
COMMIT;

-- Output success message
DO $$
BEGIN
    RAISE NOTICE 'Credit Card Phase 3 data migration completed successfully';
END $$;
