-- Create smart_notifications table
CREATE TABLE IF NOT EXISTS smart_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    category VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL DEFAULT 'medium',
    trigger VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    action_text VARCHAR(100),
    action_url VARCHAR(500),
    metadata JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT FALSE,
    is_dismissed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    scheduled_for TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_smart_notifications_user_id ON smart_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_smart_notifications_category ON smart_notifications(category);
CREATE INDEX IF NOT EXISTS idx_smart_notifications_priority ON smart_notifications(priority);
CREATE INDEX IF NOT EXISTS idx_smart_notifications_is_read ON smart_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_smart_notifications_is_dismissed ON smart_notifications(is_dismissed);
CREATE INDEX IF NOT EXISTS idx_smart_notifications_created_at ON smart_notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_smart_notifications_scheduled_for ON smart_notifications(scheduled_for);

-- Enable Row Level Security
ALTER TABLE smart_notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own notifications" ON smart_notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own notifications" ON smart_notifications
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON smart_notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own notifications" ON smart_notifications
    FOR DELETE USING (auth.uid() = user_id);

-- Create automation_rules table
CREATE TABLE IF NOT EXISTS automation_rules (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    trigger_type VARCHAR(50) NOT NULL,
    trigger_conditions JSONB NOT NULL DEFAULT '{}',
    action_type VARCHAR(50) NOT NULL,
    action_config JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for automation_rules
CREATE INDEX IF NOT EXISTS idx_automation_rules_user_id ON automation_rules(user_id);
CREATE INDEX IF NOT EXISTS idx_automation_rules_trigger_type ON automation_rules(trigger_type);
CREATE INDEX IF NOT EXISTS idx_automation_rules_is_active ON automation_rules(is_active);

-- Enable RLS for automation_rules
ALTER TABLE automation_rules ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for automation_rules
CREATE POLICY "Users can manage their own automation rules" ON automation_rules
    FOR ALL USING (auth.uid() = user_id);

-- Create notification_preferences table
CREATE TABLE IF NOT EXISTS notification_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    category VARCHAR(50) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    delivery_method VARCHAR(20) DEFAULT 'in_app',
    frequency VARCHAR(20) DEFAULT 'immediate',
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, category)
);

-- Create indexes for notification_preferences
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_id ON notification_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_category ON notification_preferences(category);

-- Enable RLS for notification_preferences
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for notification_preferences
CREATE POLICY "Users can manage their own notification preferences" ON notification_preferences
    FOR ALL USING (auth.uid() = user_id);

-- Insert default notification preferences for existing users
INSERT INTO notification_preferences (user_id, category, enabled, delivery_method, frequency)
SELECT 
    id as user_id,
    category,
    TRUE as enabled,
    'in_app' as delivery_method,
    'immediate' as frequency
FROM auth.users
CROSS JOIN (
    VALUES 
        ('budget_alerts'),
        ('spending_insights'),
        ('goal_updates'),
        ('bill_reminders'),
        ('investment_updates'),
        ('security_alerts')
) AS categories(category)
ON CONFLICT (user_id, category) DO NOTHING;

-- Create function to automatically create notification preferences for new users
CREATE OR REPLACE FUNCTION create_default_notification_preferences()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO notification_preferences (user_id, category, enabled, delivery_method, frequency)
    VALUES 
        (NEW.id, 'budget_alerts', TRUE, 'in_app', 'immediate'),
        (NEW.id, 'spending_insights', TRUE, 'in_app', 'immediate'),
        (NEW.id, 'goal_updates', TRUE, 'in_app', 'immediate'),
        (NEW.id, 'bill_reminders', TRUE, 'in_app', 'immediate'),
        (NEW.id, 'investment_updates', TRUE, 'in_app', 'immediate'),
        (NEW.id, 'security_alerts', TRUE, 'in_app', 'immediate');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically create notification preferences for new users
CREATE TRIGGER create_notification_preferences_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION create_default_notification_preferences();
