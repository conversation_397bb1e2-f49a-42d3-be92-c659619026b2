-- Telegram Integration Migration
-- This migration adds tables and functions for Telegram bot integration

-- Create telegram_users table for linking Telegram accounts to FiNManageR users
CREATE TABLE IF NOT EXISTS telegram_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  telegram_id BIGINT UNIQUE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  username TEXT,
  first_name TEXT,
  last_name TEXT,
  language_code TEXT DEFAULT 'en',
  linked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create telegram_auth_codes table for secure account linking
CREATE TABLE IF NOT EXISTS telegram_auth_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code TEXT UNIQUE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used_at TIMESTAMP WITH TIME ZONE,
  is_used BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create telegram_sessions table for bot session management
CREATE TABLE IF NOT EXISTS telegram_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  telegram_user_id UUID REFERENCES telegram_users(id) ON DELETE CASCADE,
  session_token TEXT UNIQUE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create bot_interactions table for analytics and debugging
CREATE TABLE IF NOT EXISTS bot_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  telegram_user_id UUID REFERENCES telegram_users(id) ON DELETE SET NULL,
  telegram_id BIGINT,
  command TEXT,
  message_text TEXT,
  success BOOLEAN DEFAULT true,
  error_message TEXT,
  processing_time_ms INTEGER,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add source tracking to transactions table
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS source_type TEXT DEFAULT 'web',
ADD COLUMN IF NOT EXISTS source_metadata JSONB DEFAULT '{}';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_telegram_users_telegram_id ON telegram_users(telegram_id);
CREATE INDEX IF NOT EXISTS idx_telegram_users_user_id ON telegram_users(user_id);
CREATE INDEX IF NOT EXISTS idx_telegram_users_active ON telegram_users(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_telegram_auth_codes_code ON telegram_auth_codes(code);
CREATE INDEX IF NOT EXISTS idx_telegram_auth_codes_user_id ON telegram_auth_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_telegram_auth_codes_expires ON telegram_auth_codes(expires_at);

CREATE INDEX IF NOT EXISTS idx_telegram_sessions_token ON telegram_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_telegram_sessions_telegram_user ON telegram_sessions(telegram_user_id);
CREATE INDEX IF NOT EXISTS idx_telegram_sessions_active ON telegram_sessions(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_bot_interactions_telegram_user ON bot_interactions(telegram_user_id);
CREATE INDEX IF NOT EXISTS idx_bot_interactions_created_at ON bot_interactions(created_at);

CREATE INDEX IF NOT EXISTS idx_transactions_source_type ON transactions(source_type);

-- Enable Row Level Security
ALTER TABLE telegram_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE telegram_auth_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE telegram_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE bot_interactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for telegram_users
CREATE POLICY "Users can view own telegram account" ON telegram_users
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own telegram account" ON telegram_users
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage telegram users" ON telegram_users
  FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for telegram_auth_codes
CREATE POLICY "Users can view own auth codes" ON telegram_auth_codes
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own auth codes" ON telegram_auth_codes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Service role can manage auth codes" ON telegram_auth_codes
  FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for telegram_sessions
CREATE POLICY "Service role can manage sessions" ON telegram_sessions
  FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for bot_interactions
CREATE POLICY "Users can view own bot interactions" ON bot_interactions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM telegram_users 
      WHERE telegram_users.id = bot_interactions.telegram_user_id 
      AND telegram_users.user_id = auth.uid()
    )
  );

CREATE POLICY "Service role can manage bot interactions" ON bot_interactions
  FOR ALL USING (auth.role() = 'service_role');

-- Function to generate auth code
CREATE OR REPLACE FUNCTION generate_telegram_auth_code(p_user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_code TEXT;
  v_exists BOOLEAN;
BEGIN
  -- Generate a 6-character alphanumeric code
  LOOP
    v_code := upper(substring(md5(random()::text) from 1 for 6));
    
    -- Check if code already exists
    SELECT EXISTS(
      SELECT 1 FROM telegram_auth_codes 
      WHERE code = v_code AND expires_at > NOW()
    ) INTO v_exists;
    
    EXIT WHEN NOT v_exists;
  END LOOP;
  
  -- Deactivate any existing unused codes for this user
  UPDATE telegram_auth_codes 
  SET is_used = true, used_at = NOW()
  WHERE user_id = p_user_id AND is_used = false;
  
  -- Insert new auth code (expires in 5 minutes)
  INSERT INTO telegram_auth_codes (code, user_id, expires_at)
  VALUES (v_code, p_user_id, NOW() + INTERVAL '5 minutes');
  
  RETURN v_code;
END;
$$;

-- Function to verify and use auth code
CREATE OR REPLACE FUNCTION verify_telegram_auth_code(p_code TEXT)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
BEGIN
  -- Get user_id for valid, unused, non-expired code
  SELECT user_id INTO v_user_id
  FROM telegram_auth_codes
  WHERE code = p_code 
    AND is_used = false 
    AND expires_at > NOW();
  
  -- If code is valid, mark it as used
  IF v_user_id IS NOT NULL THEN
    UPDATE telegram_auth_codes
    SET is_used = true, used_at = NOW()
    WHERE code = p_code;
  END IF;
  
  RETURN v_user_id;
END;
$$;

-- Function to link telegram account
CREATE OR REPLACE FUNCTION link_telegram_account(
  p_telegram_id BIGINT,
  p_user_id UUID,
  p_username TEXT DEFAULT NULL,
  p_first_name TEXT DEFAULT NULL,
  p_last_name TEXT DEFAULT NULL,
  p_language_code TEXT DEFAULT 'en'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_telegram_user_id UUID;
BEGIN
  -- Check if telegram account is already linked
  SELECT id INTO v_telegram_user_id
  FROM telegram_users
  WHERE telegram_id = p_telegram_id AND is_active = true;
  
  IF v_telegram_user_id IS NOT NULL THEN
    RAISE EXCEPTION 'Telegram account already linked';
  END IF;
  
  -- Check if user already has a linked telegram account
  SELECT id INTO v_telegram_user_id
  FROM telegram_users
  WHERE user_id = p_user_id AND is_active = true;
  
  IF v_telegram_user_id IS NOT NULL THEN
    -- Deactivate existing link
    UPDATE telegram_users 
    SET is_active = false, updated_at = NOW()
    WHERE id = v_telegram_user_id;
  END IF;
  
  -- Create new telegram user link
  INSERT INTO telegram_users (
    telegram_id, user_id, username, first_name, last_name, language_code
  ) VALUES (
    p_telegram_id, p_user_id, p_username, p_first_name, p_last_name, p_language_code
  ) RETURNING id INTO v_telegram_user_id;
  
  RETURN v_telegram_user_id;
END;
$$;

-- Function to unlink telegram account
CREATE OR REPLACE FUNCTION unlink_telegram_account(p_telegram_id BIGINT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_affected_rows INTEGER;
BEGIN
  -- Deactivate telegram user link
  UPDATE telegram_users 
  SET is_active = false, updated_at = NOW()
  WHERE telegram_id = p_telegram_id AND is_active = true;
  
  GET DIAGNOSTICS v_affected_rows = ROW_COUNT;
  
  -- Deactivate all sessions for this telegram user
  UPDATE telegram_sessions 
  SET is_active = false
  WHERE telegram_user_id IN (
    SELECT id FROM telegram_users WHERE telegram_id = p_telegram_id
  );
  
  RETURN v_affected_rows > 0;
END;
$$;

-- Function to log bot interaction
CREATE OR REPLACE FUNCTION log_bot_interaction(
  p_telegram_id BIGINT,
  p_command TEXT DEFAULT NULL,
  p_message_text TEXT DEFAULT NULL,
  p_success BOOLEAN DEFAULT true,
  p_error_message TEXT DEFAULT NULL,
  p_processing_time_ms INTEGER DEFAULT NULL,
  p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_telegram_user_id UUID;
  v_interaction_id UUID;
BEGIN
  -- Get telegram user id if linked
  SELECT id INTO v_telegram_user_id
  FROM telegram_users
  WHERE telegram_id = p_telegram_id AND is_active = true;
  
  -- Insert interaction log
  INSERT INTO bot_interactions (
    telegram_user_id, telegram_id, command, message_text, 
    success, error_message, processing_time_ms, metadata
  ) VALUES (
    v_telegram_user_id, p_telegram_id, p_command, p_message_text,
    p_success, p_error_message, p_processing_time_ms, p_metadata
  ) RETURNING id INTO v_interaction_id;
  
  RETURN v_interaction_id;
END;
$$;

-- Function to clean up expired auth codes and sessions
CREATE OR REPLACE FUNCTION cleanup_telegram_expired_data()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_cleaned_codes INTEGER;
  v_cleaned_sessions INTEGER;
  v_total_cleaned INTEGER;
BEGIN
  -- Clean up expired auth codes
  DELETE FROM telegram_auth_codes
  WHERE expires_at < NOW() - INTERVAL '1 day';
  GET DIAGNOSTICS v_cleaned_codes = ROW_COUNT;
  
  -- Clean up expired sessions
  UPDATE telegram_sessions
  SET is_active = false
  WHERE expires_at < NOW() AND is_active = true;
  GET DIAGNOSTICS v_cleaned_sessions = ROW_COUNT;
  
  v_total_cleaned := v_cleaned_codes + v_cleaned_sessions;
  
  RETURN v_total_cleaned;
END;
$$;

-- Create a trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
CREATE TRIGGER update_telegram_users_updated_at 
  BEFORE UPDATE ON telegram_users 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- Insert initial data or configurations if needed
-- (None required for Phase 1)

-- Add comments for documentation
COMMENT ON TABLE telegram_users IS 'Links Telegram accounts to FiNManageR users';
COMMENT ON TABLE telegram_auth_codes IS 'Temporary codes for secure account linking';
COMMENT ON TABLE telegram_sessions IS 'Bot session management';
COMMENT ON TABLE bot_interactions IS 'Analytics and debugging for bot interactions';

COMMENT ON FUNCTION generate_telegram_auth_code(UUID) IS 'Generates a secure 6-character auth code for account linking';
COMMENT ON FUNCTION verify_telegram_auth_code(TEXT) IS 'Verifies and consumes an auth code, returning user_id if valid';
COMMENT ON FUNCTION link_telegram_account(BIGINT, UUID, TEXT, TEXT, TEXT, TEXT) IS 'Links a Telegram account to a FiNManageR user';
COMMENT ON FUNCTION unlink_telegram_account(BIGINT) IS 'Unlinks a Telegram account';
COMMENT ON FUNCTION log_bot_interaction(BIGINT, TEXT, TEXT, BOOLEAN, TEXT, INTEGER, JSONB) IS 'Logs bot interactions for analytics';
COMMENT ON FUNCTION cleanup_telegram_expired_data() IS 'Cleans up expired auth codes and sessions';
