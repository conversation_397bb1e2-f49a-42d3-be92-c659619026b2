/*
  # Complete Credit Card Enhancement Plan Database Schema

  This migration ensures all required tables and columns exist for the
  Credit Card Enhancement Plan implementation.

  Tables to create/update:
  1. credit_cards (with all Phase 3 columns)
  2. credit_scores
  3. credit_card_rewards
  4. credit_card_features
  5. credit_card_comparison_features
  6. smart_notifications
  7. automation_rules
  8. automation_executions
  9. payment_methods
  10. user_behavior_analytics
  11. notification_preferences
*/

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Update credit_cards table with all Phase 3 columns
DO $$
BEGIN
  -- Add missing columns to credit_cards table
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'credit_cards' AND column_name = 'rewards_rate') THEN
    ALTER TABLE credit_cards ADD COLUMN rewards_rate DECIMAL(5, 2);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'credit_cards' AND column_name = 'foreign_transaction_fee') THEN
    ALTER TABLE credit_cards ADD COLUMN foreign_transaction_fee DECIMAL(5, 2);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'credit_cards' AND column_name = 'late_payment_fee') THEN
    ALTER TABLE credit_cards ADD COLUMN late_payment_fee DECIMAL(10, 2);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'credit_cards' AND column_name = 'min_payment_percentage') THEN
    ALTER TABLE credit_cards ADD COLUMN min_payment_percentage DECIMAL(5, 2);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'credit_cards' AND column_name = 'grace_period') THEN
    ALTER TABLE credit_cards ADD COLUMN grace_period INTEGER;
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'credit_cards' AND column_name = 'benefits') THEN
    ALTER TABLE credit_cards ADD COLUMN benefits JSONB DEFAULT '{}'::jsonb;
  END IF;
END $$;

-- 2. Create credit_scores table
CREATE TABLE IF NOT EXISTS credit_scores (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  score INTEGER NOT NULL CHECK (score >= 300 AND score <= 900),
  source VARCHAR(50) NOT NULL,
  date DATE NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for credit_scores
CREATE INDEX IF NOT EXISTS credit_scores_user_id_date_idx ON credit_scores(user_id, date);

-- Enable RLS for credit_scores
ALTER TABLE credit_scores ENABLE ROW LEVEL SECURITY;

-- Create policies for credit_scores
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_scores' AND policyname = 'Users can view own credit scores') THEN
    CREATE POLICY "Users can view own credit scores" ON credit_scores FOR SELECT USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_scores' AND policyname = 'Users can insert own credit scores') THEN
    CREATE POLICY "Users can insert own credit scores" ON credit_scores FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_scores' AND policyname = 'Users can update own credit scores') THEN
    CREATE POLICY "Users can update own credit scores" ON credit_scores FOR UPDATE USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_scores' AND policyname = 'Users can delete own credit scores') THEN
    CREATE POLICY "Users can delete own credit scores" ON credit_scores FOR DELETE USING (auth.uid() = user_id);
  END IF;
END $$;

-- 3. Create credit_card_rewards table
CREATE TABLE IF NOT EXISTS credit_card_rewards (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  credit_card_id UUID NOT NULL REFERENCES credit_cards(id) ON DELETE CASCADE,
  reward_type VARCHAR(20) NOT NULL CHECK (reward_type IN ('cashback', 'points', 'miles', 'other')),
  amount DECIMAL(12, 2) NOT NULL CHECK (amount > 0),
  description TEXT NOT NULL,
  date DATE NOT NULL,
  category VARCHAR(50),
  is_redeemed BOOLEAN DEFAULT FALSE,
  redeemed_date DATE,
  redeemed_value DECIMAL(12, 2),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for credit_card_rewards
CREATE INDEX IF NOT EXISTS credit_card_rewards_user_id_idx ON credit_card_rewards(user_id);
CREATE INDEX IF NOT EXISTS credit_card_rewards_credit_card_id_idx ON credit_card_rewards(credit_card_id);
CREATE INDEX IF NOT EXISTS credit_card_rewards_date_idx ON credit_card_rewards(date);

-- Enable RLS for credit_card_rewards
ALTER TABLE credit_card_rewards ENABLE ROW LEVEL SECURITY;

-- Create policies for credit_card_rewards
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_card_rewards' AND policyname = 'Users can view own rewards') THEN
    CREATE POLICY "Users can view own rewards" ON credit_card_rewards FOR SELECT USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_card_rewards' AND policyname = 'Users can insert own rewards') THEN
    CREATE POLICY "Users can insert own rewards" ON credit_card_rewards FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_card_rewards' AND policyname = 'Users can update own rewards') THEN
    CREATE POLICY "Users can update own rewards" ON credit_card_rewards FOR UPDATE USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_card_rewards' AND policyname = 'Users can delete own rewards') THEN
    CREATE POLICY "Users can delete own rewards" ON credit_card_rewards FOR DELETE USING (auth.uid() = user_id);
  END IF;
END $$;

-- 4. Create credit_card_comparison_features table
CREATE TABLE IF NOT EXISTS credit_card_comparison_features (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  importance VARCHAR(10) CHECK (importance IN ('high', 'medium', 'low')) DEFAULT 'medium',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, name)
);

-- Create index for credit_card_comparison_features
CREATE INDEX IF NOT EXISTS credit_card_comparison_features_user_id_idx ON credit_card_comparison_features(user_id);

-- Enable RLS for credit_card_comparison_features
ALTER TABLE credit_card_comparison_features ENABLE ROW LEVEL SECURITY;

-- Create policies for credit_card_comparison_features
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_card_comparison_features' AND policyname = 'Users can view own comparison features') THEN
    CREATE POLICY "Users can view own comparison features" ON credit_card_comparison_features FOR SELECT USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_card_comparison_features' AND policyname = 'Users can insert own comparison features') THEN
    CREATE POLICY "Users can insert own comparison features" ON credit_card_comparison_features FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_card_comparison_features' AND policyname = 'Users can update own comparison features') THEN
    CREATE POLICY "Users can update own comparison features" ON credit_card_comparison_features FOR UPDATE USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_card_comparison_features' AND policyname = 'Users can delete own comparison features') THEN
    CREATE POLICY "Users can delete own comparison features" ON credit_card_comparison_features FOR DELETE USING (auth.uid() = user_id);
  END IF;
END $$;

-- 5. Create credit_card_features table
CREATE TABLE IF NOT EXISTS credit_card_features (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  credit_card_id UUID NOT NULL REFERENCES credit_cards(id) ON DELETE CASCADE,
  feature_id UUID NOT NULL REFERENCES credit_card_comparison_features(id) ON DELETE CASCADE,
  value TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(credit_card_id, feature_id)
);

-- Create index for credit_card_features
CREATE INDEX IF NOT EXISTS credit_card_features_credit_card_id_idx ON credit_card_features(credit_card_id);
CREATE INDEX IF NOT EXISTS credit_card_features_feature_id_idx ON credit_card_features(feature_id);

-- Enable RLS for credit_card_features
ALTER TABLE credit_card_features ENABLE ROW LEVEL SECURITY;

-- Create policies for credit_card_features (based on credit card ownership)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_card_features' AND policyname = 'Users can view features of own cards') THEN
    CREATE POLICY "Users can view features of own cards" ON credit_card_features FOR SELECT
    USING (EXISTS (SELECT 1 FROM credit_cards WHERE credit_cards.id = credit_card_features.credit_card_id AND credit_cards.user_id = auth.uid()));
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_card_features' AND policyname = 'Users can insert features for own cards') THEN
    CREATE POLICY "Users can insert features for own cards" ON credit_card_features FOR INSERT
    WITH CHECK (EXISTS (SELECT 1 FROM credit_cards WHERE credit_cards.id = credit_card_features.credit_card_id AND credit_cards.user_id = auth.uid()));
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_card_features' AND policyname = 'Users can update features of own cards') THEN
    CREATE POLICY "Users can update features of own cards" ON credit_card_features FOR UPDATE
    USING (EXISTS (SELECT 1 FROM credit_cards WHERE credit_cards.id = credit_card_features.credit_card_id AND credit_cards.user_id = auth.uid()));
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'credit_card_features' AND policyname = 'Users can delete features of own cards') THEN
    CREATE POLICY "Users can delete features of own cards" ON credit_card_features FOR DELETE
    USING (EXISTS (SELECT 1 FROM credit_cards WHERE credit_cards.id = credit_card_features.credit_card_id AND credit_cards.user_id = auth.uid()));
  END IF;
END $$;

-- 6. Create smart_notifications table
CREATE TABLE IF NOT EXISTS smart_notifications (
  id VARCHAR(255) PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  priority VARCHAR(10) CHECK (priority IN ('low', 'medium', 'high', 'urgent')) DEFAULT 'medium',
  status VARCHAR(20) CHECK (status IN ('pending', 'sent', 'read', 'dismissed', 'failed')) DEFAULT 'pending',
  scheduled_for TIMESTAMPTZ,
  sent_at TIMESTAMPTZ,
  read_at TIMESTAMPTZ,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for smart_notifications
CREATE INDEX IF NOT EXISTS smart_notifications_user_id_idx ON smart_notifications(user_id);
CREATE INDEX IF NOT EXISTS smart_notifications_status_idx ON smart_notifications(status);
CREATE INDEX IF NOT EXISTS smart_notifications_scheduled_for_idx ON smart_notifications(scheduled_for);

-- Enable RLS for smart_notifications
ALTER TABLE smart_notifications ENABLE ROW LEVEL SECURITY;

-- Create policies for smart_notifications
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'smart_notifications' AND policyname = 'Users can view own notifications') THEN
    CREATE POLICY "Users can view own notifications" ON smart_notifications FOR SELECT USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'smart_notifications' AND policyname = 'Users can insert own notifications') THEN
    CREATE POLICY "Users can insert own notifications" ON smart_notifications FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'smart_notifications' AND policyname = 'Users can update own notifications') THEN
    CREATE POLICY "Users can update own notifications" ON smart_notifications FOR UPDATE USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'smart_notifications' AND policyname = 'Users can delete own notifications') THEN
    CREATE POLICY "Users can delete own notifications" ON smart_notifications FOR DELETE USING (auth.uid() = user_id);
  END IF;
END $$;

-- 7. Create automation_rules table
CREATE TABLE IF NOT EXISTS automation_rules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  rule_type VARCHAR(50) NOT NULL,
  conditions JSONB NOT NULL DEFAULT '{}'::jsonb,
  actions JSONB NOT NULL DEFAULT '{}'::jsonb,
  is_active BOOLEAN DEFAULT TRUE,
  priority INTEGER DEFAULT 1,
  execution_count INTEGER DEFAULT 0,
  last_executed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for automation_rules
CREATE INDEX IF NOT EXISTS automation_rules_user_id_idx ON automation_rules(user_id);
CREATE INDEX IF NOT EXISTS automation_rules_is_active_idx ON automation_rules(is_active);
CREATE INDEX IF NOT EXISTS automation_rules_rule_type_idx ON automation_rules(rule_type);

-- Enable RLS for automation_rules
ALTER TABLE automation_rules ENABLE ROW LEVEL SECURITY;

-- Create policies for automation_rules
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'automation_rules' AND policyname = 'Users can view own automation rules') THEN
    CREATE POLICY "Users can view own automation rules" ON automation_rules FOR SELECT USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'automation_rules' AND policyname = 'Users can insert own automation rules') THEN
    CREATE POLICY "Users can insert own automation rules" ON automation_rules FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'automation_rules' AND policyname = 'Users can update own automation rules') THEN
    CREATE POLICY "Users can update own automation rules" ON automation_rules FOR UPDATE USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'automation_rules' AND policyname = 'Users can delete own automation rules') THEN
    CREATE POLICY "Users can delete own automation rules" ON automation_rules FOR DELETE USING (auth.uid() = user_id);
  END IF;
END $$;

-- 8. Create automation_executions table
CREATE TABLE IF NOT EXISTS automation_executions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  automation_rule_id UUID NOT NULL REFERENCES automation_rules(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status VARCHAR(20) CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')) DEFAULT 'pending',
  trigger_data JSONB DEFAULT '{}'::jsonb,
  execution_result JSONB DEFAULT '{}'::jsonb,
  error_message TEXT,
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for automation_executions
CREATE INDEX IF NOT EXISTS automation_executions_automation_rule_id_idx ON automation_executions(automation_rule_id);
CREATE INDEX IF NOT EXISTS automation_executions_user_id_idx ON automation_executions(user_id);
CREATE INDEX IF NOT EXISTS automation_executions_status_idx ON automation_executions(status);
CREATE INDEX IF NOT EXISTS automation_executions_started_at_idx ON automation_executions(started_at);

-- Enable RLS for automation_executions
ALTER TABLE automation_executions ENABLE ROW LEVEL SECURITY;

-- Create policies for automation_executions
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'automation_executions' AND policyname = 'Users can view own automation executions') THEN
    CREATE POLICY "Users can view own automation executions" ON automation_executions FOR SELECT USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'automation_executions' AND policyname = 'Users can insert own automation executions') THEN
    CREATE POLICY "Users can insert own automation executions" ON automation_executions FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'automation_executions' AND policyname = 'Users can update own automation executions') THEN
    CREATE POLICY "Users can update own automation executions" ON automation_executions FOR UPDATE USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'automation_executions' AND policyname = 'Users can delete own automation executions') THEN
    CREATE POLICY "Users can delete own automation executions" ON automation_executions FOR DELETE USING (auth.uid() = user_id);
  END IF;
END $$;

-- 9. Create payment_methods table (if not exists)
CREATE TABLE IF NOT EXISTS payment_methods (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL CHECK (type IN ('credit_card', 'debit_card', 'bank_account', 'cash', 'digital_wallet', 'other')),
  account_number_last4 VARCHAR(4),
  bank_name VARCHAR(255),
  status VARCHAR(20) CHECK (status IN ('active', 'inactive', 'expired')) DEFAULT 'active',
  is_default BOOLEAN DEFAULT FALSE,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for payment_methods
CREATE INDEX IF NOT EXISTS payment_methods_user_id_idx ON payment_methods(user_id);
CREATE INDEX IF NOT EXISTS payment_methods_status_idx ON payment_methods(status);

-- Enable RLS for payment_methods
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;

-- Create policies for payment_methods
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'payment_methods' AND policyname = 'Users can view own payment methods') THEN
    CREATE POLICY "Users can view own payment methods" ON payment_methods FOR SELECT USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'payment_methods' AND policyname = 'Users can insert own payment methods') THEN
    CREATE POLICY "Users can insert own payment methods" ON payment_methods FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'payment_methods' AND policyname = 'Users can update own payment methods') THEN
    CREATE POLICY "Users can update own payment methods" ON payment_methods FOR UPDATE USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'payment_methods' AND policyname = 'Users can delete own payment methods') THEN
    CREATE POLICY "Users can delete own payment methods" ON payment_methods FOR DELETE USING (auth.uid() = user_id);
  END IF;
END $$;

-- 10. Create user_behavior_analytics table
CREATE TABLE IF NOT EXISTS user_behavior_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  event_type VARCHAR(100) NOT NULL,
  event_data JSONB DEFAULT '{}'::jsonb,
  session_id VARCHAR(255),
  user_agent TEXT,
  ip_address INET,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for user_behavior_analytics
CREATE INDEX IF NOT EXISTS user_behavior_analytics_user_id_idx ON user_behavior_analytics(user_id);
CREATE INDEX IF NOT EXISTS user_behavior_analytics_event_type_idx ON user_behavior_analytics(event_type);
CREATE INDEX IF NOT EXISTS user_behavior_analytics_timestamp_idx ON user_behavior_analytics(timestamp);

-- Enable RLS for user_behavior_analytics
ALTER TABLE user_behavior_analytics ENABLE ROW LEVEL SECURITY;

-- Create policies for user_behavior_analytics
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_behavior_analytics' AND policyname = 'Users can view own behavior analytics') THEN
    CREATE POLICY "Users can view own behavior analytics" ON user_behavior_analytics FOR SELECT USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_behavior_analytics' AND policyname = 'Users can insert own behavior analytics') THEN
    CREATE POLICY "Users can insert own behavior analytics" ON user_behavior_analytics FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_behavior_analytics' AND policyname = 'Users can update own behavior analytics') THEN
    CREATE POLICY "Users can update own behavior analytics" ON user_behavior_analytics FOR UPDATE USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_behavior_analytics' AND policyname = 'Users can delete own behavior analytics') THEN
    CREATE POLICY "Users can delete own behavior analytics" ON user_behavior_analytics FOR DELETE USING (auth.uid() = user_id);
  END IF;
END $$;

-- 11. Create notification_preferences table
CREATE TABLE IF NOT EXISTS notification_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  notification_type VARCHAR(100) NOT NULL,
  enabled BOOLEAN DEFAULT TRUE,
  delivery_method VARCHAR(50) CHECK (delivery_method IN ('email', 'push', 'sms', 'in_app')) DEFAULT 'in_app',
  frequency VARCHAR(50) CHECK (frequency IN ('immediate', 'daily', 'weekly', 'monthly')) DEFAULT 'immediate',
  quiet_hours_start TIME,
  quiet_hours_end TIME,
  timezone VARCHAR(50),
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, notification_type, delivery_method)
);

-- Create indexes for notification_preferences
CREATE INDEX IF NOT EXISTS notification_preferences_user_id_idx ON notification_preferences(user_id);
CREATE INDEX IF NOT EXISTS notification_preferences_notification_type_idx ON notification_preferences(notification_type);

-- Enable RLS for notification_preferences
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;

-- Create policies for notification_preferences
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'notification_preferences' AND policyname = 'Users can view own notification preferences') THEN
    CREATE POLICY "Users can view own notification preferences" ON notification_preferences FOR SELECT USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'notification_preferences' AND policyname = 'Users can insert own notification preferences') THEN
    CREATE POLICY "Users can insert own notification preferences" ON notification_preferences FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'notification_preferences' AND policyname = 'Users can update own notification preferences') THEN
    CREATE POLICY "Users can update own notification preferences" ON notification_preferences FOR UPDATE USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'notification_preferences' AND policyname = 'Users can delete own notification preferences') THEN
    CREATE POLICY "Users can delete own notification preferences" ON notification_preferences FOR DELETE USING (auth.uid() = user_id);
  END IF;
END $$;

-- 12. Create notification_analytics table
CREATE TABLE IF NOT EXISTS notification_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  notification_id VARCHAR(255),
  notification_type VARCHAR(100) NOT NULL,
  event_type VARCHAR(50) CHECK (event_type IN ('sent', 'delivered', 'opened', 'clicked', 'dismissed', 'failed')) NOT NULL,
  delivery_method VARCHAR(50),
  metadata JSONB DEFAULT '{}'::jsonb,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for notification_analytics
CREATE INDEX IF NOT EXISTS notification_analytics_user_id_idx ON notification_analytics(user_id);
CREATE INDEX IF NOT EXISTS notification_analytics_notification_id_idx ON notification_analytics(notification_id);
CREATE INDEX IF NOT EXISTS notification_analytics_event_type_idx ON notification_analytics(event_type);
CREATE INDEX IF NOT EXISTS notification_analytics_timestamp_idx ON notification_analytics(timestamp);

-- Enable RLS for notification_analytics
ALTER TABLE notification_analytics ENABLE ROW LEVEL SECURITY;

-- Create policies for notification_analytics
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'notification_analytics' AND policyname = 'Users can view own notification analytics') THEN
    CREATE POLICY "Users can view own notification analytics" ON notification_analytics FOR SELECT USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'notification_analytics' AND policyname = 'Users can insert own notification analytics') THEN
    CREATE POLICY "Users can insert own notification analytics" ON notification_analytics FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'notification_analytics' AND policyname = 'Users can update own notification analytics') THEN
    CREATE POLICY "Users can update own notification analytics" ON notification_analytics FOR UPDATE USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'notification_analytics' AND policyname = 'Users can delete own notification analytics') THEN
    CREATE POLICY "Users can delete own notification analytics" ON notification_analytics FOR DELETE USING (auth.uid() = user_id);
  END IF;
END $$;

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'Complete Credit Card Enhancement Plan database schema migration completed successfully!';
  RAISE NOTICE 'All required tables and columns have been created or updated.';
END $$;
