-- Fix Telegram Integration Database Issues
-- This migration ensures all required tables and functions exist with proper permissions

-- Create telegram_users table if it doesn't exist
CREATE TABLE IF NOT EXISTS telegram_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  telegram_id BIGINT UNIQUE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  username TEXT,
  first_name TEXT,
  last_name TEXT,
  language_code TEXT DEFAULT 'en',
  linked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_permanent_auth_codes table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_permanent_auth_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  auth_code TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);

-- Create telegram_auth_codes table if it doesn't exist (for temporary codes)
CREATE TABLE IF NOT EXISTS telegram_auth_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  auth_code TEXT UNIQUE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_used BOOLEAN DEFAULT false
);

-- Enable RLS on all tables
ALTER TABLE telegram_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_permanent_auth_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE telegram_auth_codes ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own telegram data" ON telegram_users;
DROP POLICY IF EXISTS "Users can insert their own telegram data" ON telegram_users;
DROP POLICY IF EXISTS "Users can update their own telegram data" ON telegram_users;
DROP POLICY IF EXISTS "Users can delete their own telegram data" ON telegram_users;
DROP POLICY IF EXISTS "Service role can manage all telegram data" ON telegram_users;

DROP POLICY IF EXISTS "Users can view their own auth codes" ON user_permanent_auth_codes;
DROP POLICY IF EXISTS "Users can insert their own auth codes" ON user_permanent_auth_codes;
DROP POLICY IF EXISTS "Users can update their own auth codes" ON user_permanent_auth_codes;
DROP POLICY IF EXISTS "Service role can manage all auth codes" ON user_permanent_auth_codes;

DROP POLICY IF EXISTS "Users can view their own temp auth codes" ON telegram_auth_codes;
DROP POLICY IF EXISTS "Users can insert their own temp auth codes" ON telegram_auth_codes;
DROP POLICY IF EXISTS "Users can update their own temp auth codes" ON telegram_auth_codes;
DROP POLICY IF EXISTS "Service role can manage all temp auth codes" ON telegram_auth_codes;

-- Create comprehensive RLS policies for telegram_users
CREATE POLICY "Users can view their own telegram data" ON telegram_users
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own telegram data" ON telegram_users
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own telegram data" ON telegram_users
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own telegram data" ON telegram_users
  FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all telegram data" ON telegram_users
  FOR ALL USING (auth.role() = 'service_role');

-- Create comprehensive RLS policies for user_permanent_auth_codes
CREATE POLICY "Users can view their own auth codes" ON user_permanent_auth_codes
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own auth codes" ON user_permanent_auth_codes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own auth codes" ON user_permanent_auth_codes
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all auth codes" ON user_permanent_auth_codes
  FOR ALL USING (auth.role() = 'service_role');

-- Create comprehensive RLS policies for telegram_auth_codes
CREATE POLICY "Users can view their own temp auth codes" ON telegram_auth_codes
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own temp auth codes" ON telegram_auth_codes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own temp auth codes" ON telegram_auth_codes
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all temp auth codes" ON telegram_auth_codes
  FOR ALL USING (auth.role() = 'service_role');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_telegram_users_user_id ON telegram_users(user_id);
CREATE INDEX IF NOT EXISTS idx_telegram_users_telegram_id ON telegram_users(telegram_id);
CREATE INDEX IF NOT EXISTS idx_telegram_users_is_active ON telegram_users(is_active);
CREATE INDEX IF NOT EXISTS idx_user_permanent_auth_codes_user_id ON user_permanent_auth_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_user_permanent_auth_codes_auth_code ON user_permanent_auth_codes(auth_code);
CREATE INDEX IF NOT EXISTS idx_telegram_auth_codes_user_id ON telegram_auth_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_telegram_auth_codes_auth_code ON telegram_auth_codes(auth_code);
CREATE INDEX IF NOT EXISTS idx_telegram_auth_codes_expires_at ON telegram_auth_codes(expires_at);

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON telegram_users TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_permanent_auth_codes TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON telegram_auth_codes TO authenticated;

-- Grant full permissions to service role
GRANT ALL ON telegram_users TO service_role;
GRANT ALL ON user_permanent_auth_codes TO service_role;
GRANT ALL ON telegram_auth_codes TO service_role;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO service_role;
