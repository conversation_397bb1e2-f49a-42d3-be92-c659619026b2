groups:
  - name: finmanager-phase7-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(finmanager_error_rate[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: finmanager-bot
        annotations:
          summary: "High error rate detected in FiNManageR bot"
          description: "Error rate is {{ $value }} errors per second"

      - alert: LowNLPConfidence
        expr: avg(finmanager_nlp_confidence) < 0.7
        for: 5m
        labels:
          severity: warning
          service: nlp-processing
        annotations:
          summary: "NLP confidence below threshold"
          description: "Average NLP confidence is {{ $value }}"

      - alert: HighMemoryUsage
        expr: finmanager_memory_usage_bytes > 500000000
        for: 3m
        labels:
          severity: warning
          service: finmanager-bot
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value | humanize }}B"

      - alert: SlowOCRProcessing
        expr: avg(finmanager_ocr_processing_time_seconds) > 30
        for: 2m
        labels:
          severity: warning
          service: ocr-processing
        annotations:
          summary: "OCR processing is slow"
          description: "Average OCR processing time is {{ $value }}s"

      - alert: BotDown
        expr: up{job="finmanager-bot"} == 0
        for: 1m
        labels:
          severity: critical
          service: finmanager-bot
        annotations:
          summary: "FiNManageR bot is down"
          description: "The bot has been down for more than 1 minute"

      - alert: DatabaseConnectionIssues
        expr: finmanager_database_connection_errors > 5
        for: 1m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "Database connection issues"
          description: "{{ $value }} database connection errors in the last minute"