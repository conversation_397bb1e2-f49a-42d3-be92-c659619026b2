# Prometheus Configuration for FiNManageR Phase 7
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'finmanager-bot'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: '/metrics'
    
  - job_name: 'finmanager-health'
    static_configs:
      - targets: ['localhost:3001']
    scrape_interval: 30s
    metrics_path: '/health'

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093