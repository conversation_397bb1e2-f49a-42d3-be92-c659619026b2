/**
 * Netlify Function for GCS Authentication
 * Handles JWT signing and token exchange for Google Cloud Storage
 */

const jwt = require('jsonwebtoken');

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    const { serviceAccountKey } = JSON.parse(event.body);

    if (!serviceAccountKey) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Service account key is required' })
      };
    }

    // Decode service account key
    let serviceAccount;
    try {
      const decoded = Buffer.from(serviceAccountKey, 'base64').toString('utf-8');
      serviceAccount = JSON.parse(decoded);
    } catch (error) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Invalid service account key format' })
      };
    }

    // Validate required fields
    if (!serviceAccount.client_email || !serviceAccount.private_key || !serviceAccount.project_id) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Invalid service account key - missing required fields' })
      };
    }

    // Create JWT for service account authentication
    const now = Math.floor(Date.now() / 1000);
    
    const payload = {
      iss: serviceAccount.client_email,
      scope: 'https://www.googleapis.com/auth/cloud-platform',
      aud: 'https://oauth2.googleapis.com/token',
      exp: now + 3600,
      iat: now
    };

    // Sign JWT with private key
    const token = jwt.sign(payload, serviceAccount.private_key, { 
      algorithm: 'RS256',
      header: {
        alg: 'RS256',
        typ: 'JWT'
      }
    });

    // Exchange JWT for access token
    const fetch = require('node-fetch');
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        assertion: token
      })
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('Token exchange failed:', errorText);
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ 
          error: 'Failed to exchange JWT for access token',
          details: errorText
        })
      };
    }

    const tokenData = await tokenResponse.json();
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        access_token: tokenData.access_token,
        expires_in: tokenData.expires_in
      })
    };

  } catch (error) {
    console.error('GCS auth error:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Internal server error',
        message: error.message || 'Unknown error'
      })
    };
  }
};
