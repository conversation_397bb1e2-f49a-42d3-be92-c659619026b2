-- Add telegram_interactions table for Phase 4 production bot
-- This table is used by the production bot for logging interactions

CREATE TABLE IF NOT EXISTS telegram_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  telegram_user_id TEXT NOT NULL, -- Telegram user ID as string
  command TEXT,
  description TEXT,
  success BOOLEAN DEFAULT true,
  error_message TEXT,
  processing_time_ms INTEGER,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_telegram_interactions_user_id ON telegram_interactions(telegram_user_id);
CREATE INDEX IF NOT EXISTS idx_telegram_interactions_created_at ON telegram_interactions(created_at);
CREATE INDEX IF NOT EXISTS idx_telegram_interactions_command ON telegram_interactions(command);
CREATE INDEX IF NOT EXISTS idx_telegram_interactions_success ON telegram_interactions(success);

-- Enable Row Level Security
ALTER TABLE telegram_interactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for telegram_interactions
CREATE POLICY "Service role can manage telegram interactions" ON telegram_interactions
  FOR ALL USING (auth.role() = 'service_role');

-- Grant permissions
GRANT ALL ON telegram_interactions TO service_role;

-- Add comment
COMMENT ON TABLE telegram_interactions IS 'Logs all interactions with the Telegram bot for analytics and debugging';
