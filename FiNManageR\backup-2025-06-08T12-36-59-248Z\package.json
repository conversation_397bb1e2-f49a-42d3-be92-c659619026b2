{"name": "project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build-no-type-check": "vite build", "build-with-partial-check": "tsc --noEmit --skipLib<PERSON>heck --incremental && vite build", "build-prod": "tsc --noEmit --skipLibCheck && vite build", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "type-check-strict": "tsc --noEmit", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:watch": "node --experimental-vm-modules node_modules/jest/bin/jest.js --watch", "test:coverage": "node --experimental-vm-modules node_modules/jest/bin/jest.js --coverage", "test:auth": "node --experimental-vm-modules node_modules/jest/bin/jest.js auth", "test:transactions": "node --experimental-vm-modules node_modules/jest/bin/jest.js transactions", "test:budget": "node --experimental-vm-modules node_modules/jest/bin/jest.js budget", "test:integration": "node --experimental-vm-modules node_modules/jest/bin/jest.js integration", "test:validate": "node scripts/test-setup-validator.js", "clear-cache": "node clear-cache.cjs", "clean-dev": "npm run clear-cache && npm run dev", "reset": "node reset.cjs", "db:migrate": "node scripts/run-migrations.js", "db:cleanup": "node scripts/run-database-cleanup.js", "db:monitor": "node scripts/database-monitoring.js", "auth:enhance": "node scripts/enhance-authentication.js", "security:audit": "node scripts/security-audit.js", "backend:optimize": "npm run db:cleanup && npm run auth:enhance && npm run security:audit", "backend:summary": "node scripts/backend-optimization-summary.js", "bundle:analyze": "node scripts/bundle-analyzer.js", "quality:summary": "node scripts/code-quality-summary.js", "quality:check": "npm run bundle:analyze && npm run quality:summary", "phase3:setup": "npm run db:cleanup && npm run db:migrate", "phase3:summary": "node scripts/phase3-summary.js", "week9-10:summary": "node scripts/week9-10-summary.js", "project:complete": "node scripts/project-completion-summary.js", "phase4:summary": "node scripts/phase4-summary.js", "production:deploy": "npm run build && npm run test:coverage && npm run lint", "production:monitor": "npm run dev -- --mode production", "build:check": "node scripts/build-check.js", "phase1:progress": "node scripts/phase1-progress-tracker.js", "phase2:progress": "node scripts/phase2-performance-tracker.js", "phase2:console-cleanup": "node scripts/console-cleanup.js", "mobile:test": "npm run dev -- --host 0.0.0.0", "pwa:validate": "npm run build && npx pwa-asset-generator", "setup": "npm run db:migrate && npm run dev", "setup-local": "node setup-local-env.js", "supabase": "powershell -ExecutionPolicy Bypass -File setup-supabase-cli.ps1", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "fullstack": "powershell -ExecutionPolicy Bypass -Command \"Start-Process supabase -ArgumentList 'start' -NoNewWindow; npm run dev\"", "audit:database": "node scripts/verify-database-schema.js", "audit:mobile": "node scripts/mobile-responsiveness-audit.js", "audit:accessibility": "node scripts/accessibility-audit.js", "audit:performance": "node scripts/performance-audit.js", "audit:all": "npm run audit:database && npm run audit:mobile && npm run audit:accessibility && npm run audit:performance", "clean:console": "node scripts/complete-remaining-tasks.js --console-only", "complete:tasks": "node scripts/complete-remaining-tasks.js", "setup:2fa": "node scripts/setup-2fa-database.js", "test:2fa": "echo 'Testing 2FA system...' && npm run dev", "create:admin": "node scripts/create-admin.js", "migrate:db": "node scripts/run-migration.js", "verify:tables": "node scripts/verify-tables.js", "manual:tables": "node scripts/manual-table-creation.js", "fix:rls": "node scripts/fix-rls-policies.js", "implement:all": "node scripts/master-implementation-plan.js", "implement:phase1": "node scripts/phase1-critical-fixes.js", "implement:phase2": "node scripts/phase2-performance-optimization.js", "implement:phase3": "node scripts/phase3-security-enhancements.js", "implement:phase4": "node scripts/phase4-advanced-features.js", "implement:status": "node scripts/implementation-status.js"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.28.4", "@tanstack/react-query-devtools": "^5.28.4", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cohere-ai": "^7.17.1", "date-fns": "^3.3.1", "dompurify": "^3.2.5", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "framer-motion": "^12.16.0", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "lucide-react": "^0.358.0", "marked": "^15.0.12", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.51.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.30.0", "recharts": "^2.15.3", "tailwind-merge": "^2.6.0", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/date-fns": "^2.5.3", "@types/dompurify": "^3.0.5", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/node": "^20.11.28", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/react-router-dom": "^5.3.3", "@types/testing-library__jest-dom": "^5.14.9", "@types/testing-library__react": "^10.0.1", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.35", "rollup-plugin-visualizer": "^6.0.3", "supabase": "^2.22.12", "tailwindcss": "^3.4.1", "terser": "^5.39.0", "ts-jest": "^29.3.2", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5", "vite-plugin-markdown": "^2.2.0", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-pwa": "^1.0.0", "workbox-window": "^7.3.0"}}