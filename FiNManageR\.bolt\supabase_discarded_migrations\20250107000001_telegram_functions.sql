-- Telegram Integration RPC Functions
-- This migration creates all required functions for Telegram bot integration

-- Function to generate unique 8-digit auth codes
CREATE OR REPLACE FUNCTION generate_unique_auth_code()
RETURNS TEXT AS $$
DECLARE
  new_code TEXT;
  code_exists BOOLEAN;
BEGIN
  LOOP
    -- Generate 8-digit code
    new_code := LPAD(FLOOR(RANDOM() * 100000000)::TEXT, 8, '0');
    
    -- Check if code already exists in permanent codes
    SELECT EXISTS(
      SELECT 1 FROM user_permanent_auth_codes 
      WHERE auth_code = new_code AND is_active = true
    ) INTO code_exists;
    
    -- If code doesn't exist, check temporary codes too
    IF NOT code_exists THEN
      SELECT EXISTS(
        SELECT 1 FROM telegram_auth_codes 
        WHERE auth_code = new_code AND expires_at > NOW()
      ) INTO code_exists;
    END IF;
    
    -- If code is unique, return it
    IF NOT code_exists THEN
      RETURN new_code;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get or create permanent auth code
CREATE OR REPLACE FUNCTION get_or_create_permanent_auth_code(p_user_id UUID)
RETURNS TEXT AS $$
DECLARE
  existing_code TEXT;
  new_code TEXT;
BEGIN
  -- Check if user already has an active permanent code
  SELECT auth_code INTO existing_code
  FROM user_permanent_auth_codes
  WHERE user_id = p_user_id AND is_active = true;
  
  -- If code exists, return it
  IF existing_code IS NOT NULL THEN
    RETURN existing_code;
  END IF;
  
  -- Generate new unique code
  new_code := generate_unique_auth_code();
  
  -- Insert new permanent code
  INSERT INTO user_permanent_auth_codes (user_id, auth_code, is_active)
  VALUES (p_user_id, new_code, true)
  ON CONFLICT (user_id) DO UPDATE SET
    auth_code = new_code,
    is_active = true,
    updated_at = NOW();
  
  RETURN new_code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate permanent auth code
CREATE OR REPLACE FUNCTION validate_permanent_auth_code(p_auth_code TEXT)
RETURNS TABLE(is_valid BOOLEAN, user_id UUID) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    true as is_valid,
    upac.user_id
  FROM user_permanent_auth_codes upac
  WHERE upac.auth_code = p_auth_code 
    AND upac.is_active = true;
  
  -- If no results, return false
  IF NOT FOUND THEN
    RETURN QUERY SELECT false as is_valid, NULL::UUID as user_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to regenerate permanent auth code
CREATE OR REPLACE FUNCTION regenerate_permanent_auth_code(p_user_id UUID)
RETURNS TEXT AS $$
DECLARE
  new_code TEXT;
BEGIN
  -- Generate new unique code
  new_code := generate_unique_auth_code();
  
  -- Update existing code or insert new one
  INSERT INTO user_permanent_auth_codes (user_id, auth_code, is_active)
  VALUES (p_user_id, new_code, true)
  ON CONFLICT (user_id) DO UPDATE SET
    auth_code = new_code,
    is_active = true,
    updated_at = NOW();
  
  RETURN new_code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to link Telegram account
CREATE OR REPLACE FUNCTION link_telegram_account(
  p_telegram_id BIGINT,
  p_user_id UUID,
  p_username TEXT DEFAULT NULL,
  p_first_name TEXT DEFAULT NULL,
  p_last_name TEXT DEFAULT NULL,
  p_language_code TEXT DEFAULT 'en'
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Insert or update telegram user
  INSERT INTO telegram_users (
    telegram_id, 
    user_id, 
    username, 
    first_name, 
    last_name, 
    language_code,
    is_active,
    linked_at
  )
  VALUES (
    p_telegram_id, 
    p_user_id, 
    p_username, 
    p_first_name, 
    p_last_name, 
    p_language_code,
    true,
    NOW()
  )
  ON CONFLICT (telegram_id) DO UPDATE SET
    user_id = p_user_id,
    username = p_username,
    first_name = p_first_name,
    last_name = p_last_name,
    language_code = p_language_code,
    is_active = true,
    linked_at = NOW(),
    updated_at = NOW();
  
  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to unlink Telegram account
CREATE OR REPLACE FUNCTION unlink_telegram_account(p_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Deactivate telegram user instead of deleting
  UPDATE telegram_users 
  SET is_active = false, updated_at = NOW()
  WHERE user_id = p_user_id;
  
  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get telegram user by user_id
CREATE OR REPLACE FUNCTION get_telegram_user_by_user_id(p_user_id UUID)
RETURNS TABLE(
  id UUID,
  telegram_id BIGINT,
  user_id UUID,
  username TEXT,
  first_name TEXT,
  last_name TEXT,
  language_code TEXT,
  linked_at TIMESTAMPTZ,
  is_active BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    tu.id,
    tu.telegram_id,
    tu.user_id,
    tu.username,
    tu.first_name,
    tu.last_name,
    tu.language_code,
    tu.linked_at,
    tu.is_active
  FROM telegram_users tu
  WHERE tu.user_id = p_user_id AND tu.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get telegram user by telegram_id
CREATE OR REPLACE FUNCTION get_telegram_user_by_telegram_id(p_telegram_id BIGINT)
RETURNS TABLE(
  id UUID,
  telegram_id BIGINT,
  user_id UUID,
  username TEXT,
  first_name TEXT,
  last_name TEXT,
  language_code TEXT,
  linked_at TIMESTAMPTZ,
  is_active BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    tu.id,
    tu.telegram_id,
    tu.user_id,
    tu.username,
    tu.first_name,
    tu.last_name,
    tu.language_code,
    tu.linked_at,
    tu.is_active
  FROM telegram_users tu
  WHERE tu.telegram_id = p_telegram_id AND tu.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create temporary auth code (for backwards compatibility)
CREATE OR REPLACE FUNCTION create_telegram_auth_code(p_user_id UUID)
RETURNS TEXT AS $$
DECLARE
  new_code TEXT;
BEGIN
  -- Generate 6-character temporary code
  new_code := UPPER(SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 6));
  
  -- Insert temporary code (expires in 5 minutes)
  INSERT INTO telegram_auth_codes (user_id, auth_code, expires_at)
  VALUES (p_user_id, new_code, NOW() + INTERVAL '5 minutes');
  
  RETURN new_code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate temporary auth code (for backwards compatibility)
CREATE OR REPLACE FUNCTION validate_telegram_auth_code(p_auth_code TEXT)
RETURNS TABLE(is_valid BOOLEAN, user_id UUID) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    true as is_valid,
    tac.user_id
  FROM telegram_auth_codes tac
  WHERE tac.auth_code = p_auth_code 
    AND tac.expires_at > NOW() 
    AND tac.is_used = false;
  
  -- Mark code as used
  UPDATE telegram_auth_codes 
  SET is_used = true, used_at = NOW()
  WHERE auth_code = p_auth_code;
  
  -- If no results, return false
  IF NOT FOUND THEN
    RETURN QUERY SELECT false as is_valid, NULL::UUID as user_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on all functions
GRANT EXECUTE ON FUNCTION generate_unique_auth_code() TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION get_or_create_permanent_auth_code(UUID) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION validate_permanent_auth_code(TEXT) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION regenerate_permanent_auth_code(UUID) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION link_telegram_account(BIGINT, UUID, TEXT, TEXT, TEXT, TEXT) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION unlink_telegram_account(UUID) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION get_telegram_user_by_user_id(UUID) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION get_telegram_user_by_telegram_id(BIGINT) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION create_telegram_auth_code(UUID) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION validate_telegram_auth_code(TEXT) TO authenticated, service_role;
