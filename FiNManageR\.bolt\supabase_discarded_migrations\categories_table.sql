-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
  color TEXT,
  icon TEXT,
  is_default BOOLEAN NOT NULL DEFAULT false,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE (user_id, name, type)
);

-- Add RLS policies
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Policy for users to select their own categories
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_policies WHERE tablename = 'categories' AND policyname = 'categories_select_policy'
  ) THEN
    CREATE POLICY categories_select_policy ON categories
      FOR SELECT
      USING (auth.uid() = user_id);
  END IF;
END
$$;

-- Policy for users to insert their own categories
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_policies WHERE tablename = 'categories' AND policyname = 'categories_insert_policy'
  ) THEN
    CREATE POLICY categories_insert_policy ON categories
      FOR INSERT
      WITH CHECK (auth.uid() = user_id);
  END IF;
END
$$;

-- Policy for users to update their own categories
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_policies WHERE tablename = 'categories' AND policyname = 'categories_update_policy'
  ) THEN
    CREATE POLICY categories_update_policy ON categories
      FOR UPDATE
      USING (auth.uid() = user_id);
  END IF;
END
$$;

-- Policy for users to delete their own categories
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_policies WHERE tablename = 'categories' AND policyname = 'categories_delete_policy'
  ) THEN
    CREATE POLICY categories_delete_policy ON categories
      FOR DELETE
      USING (auth.uid() = user_id);
  END IF;
END
$$;

-- Create index on user_id for faster queries
CREATE INDEX IF NOT EXISTS categories_user_id_idx ON categories(user_id);

-- Create index on type for faster filtering
CREATE INDEX IF NOT EXISTS categories_type_idx ON categories(type);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at on update
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'categories_updated_at_trigger'
  ) THEN
    CREATE TRIGGER categories_updated_at_trigger
    BEFORE UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;
END
$$;
