import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { VitePWA } from 'vite-plugin-pwa'
// Import the node polyfills plugin
import { nodePolyfills } from 'vite-plugin-node-polyfills'
// Import markdown plugin
import { plugin as markdown } from 'vite-plugin-markdown'

// Import node built-ins for Vite 6
import { fileURLToPath } from 'url'

export default defineConfig({
  // Define mode-specific configurations
  server: {
    hmr: {
      overlay: false, // Disable the HMR overlay to prevent it from hiding errors
    },
    port: 5173,
    // Force cache invalidation
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    },
    fs: {
      // Restrict file serving to project root and node_modules only
      allow: [
        // Project root
        '.',
        // Node modules
        'node_modules',
      ],
      // Explicitly deny access to sensitive directories
      deny: [
        // Deny access to dot files and directories
        '**/.env*',
        '**/.git/**',
        '**/node_modules/.vite/**',
      ],
      // Strict file serving (addresses CVE-2023-49293)
      strict: true,
    },
  },

  // Add markdown files to assetsInclude
  assetsInclude: ['**/*.md'],

  // Set the origin for connections
  origin: 'http://localhost:5173',
  // Additional security headers
  headers: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    // Update CSP to allow connections to AI services and other external APIs
    'Content-Security-Policy': `
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://unpkg.com https://cdn.jsdelivr.net;
      style-src 'self' 'unsafe-inline';
      img-src 'self' data: blob:;
      font-src 'self';
      connect-src 'self' https://*.supabase.co https://*.googleapis.com https://generativelanguage.googleapis.com https://ai.google.dev https://api.cohere.ai https://api.cohere.com https://cdnjs.cloudflare.com https://unpkg.com https://cdn.jsdelivr.net http://localhost:* ws: wss:;
      frame-src 'self' https://ai.google.dev;
      base-uri 'self';
      form-action 'self'
    `.replace(/\s+/g, ' ').trim(),
  },
  plugins: [
    react(),
    // Use the nodePolyfills plugin with specific configuration
    nodePolyfills({
      // Don't include the full Node.js polyfill
      protocolImports: true,
      // Include polyfills needed for Google Drive API
      include: ['buffer', 'process', 'stream', 'util', 'events', 'path', 'querystring', 'url', 'string_decoder', 'punycode', 'http', 'https', 'zlib', 'assert'],
    }),
    // Use the markdown plugin to handle .md files
    markdown({
      mode: 'html',
      markdownIt: {
        html: true,
        linkify: true,
        typographer: true,
      }
    }),

    VitePWA({
      registerType: 'autoUpdate',
      includeAssets: ['favicon.ico', 'apple-touch-icon.png', 'favicon.svg', 'robots.txt'],
      workbox: {
        navigateFallback: '/index.html',
        globPatterns: ['**/*.{js,css,html,ico,png,svg}'],
        runtimeCaching: [
          {
            // Cache API responses
            urlPattern: /^https:\/\/api\./i,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'api-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 // 24 hours
              },
              networkTimeoutSeconds: 10
            }
          },
          {
            // Cache font files
            urlPattern: /^https:\/\/fonts\./i,
            handler: 'StaleWhileRevalidate',
            options: {
              cacheName: 'font-cache',
              expiration: {
                maxEntries: 10,
                maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
              }
            }
          },
          {
            // Cache static assets
            urlPattern: /\.(?:png|jpg|jpeg|svg|gif)$/,
            handler: 'CacheFirst',
            options: {
              cacheName: 'image-cache',
              expiration: {
                maxEntries: 50,
                maxAgeSeconds: 60 * 60 * 24 * 7 // 7 days
              }
            }
          }
        ]
      },
      manifest: {
        name: "Sivaji's Fin Manager",
        short_name: "FinManager",
        description: "Track your expenses and manage your budget with Sivaji's Finance Manager",
        theme_color: "#3B82F6",
        background_color: "#ffffff",
        display: "standalone",
        categories: ["finance", "productivity", "utilities"],
        orientation: "portrait",
        icons: [
          {
            src: "favicon.png",
            sizes: "192x192",
            type: "image/png"
          },
          {
            src: "apple-touch-icon.png",
            sizes: "512x512",
            type: "image/png"
          },
          {
            src: "apple-touch-icon.png",
            sizes: "512x512",
            type: "image/png",
            purpose: "maskable"
          }
        ]
      }
    })
  ],
  resolve: {
    alias: {
      "@": path.resolve(path.dirname(fileURLToPath(import.meta.url)), "./src"),
    },
  },
  build: {
    target: 'es2020', // Set a single target
    sourcemap: true,
    minify: 'terser', // Use terser for better compatibility
    terserOptions: {
      compress: {
        // Disable console removal in production
        drop_console: false,
      },
      // Preserve important class and function names
      keep_classnames: true,
      keep_fnames: true,
      // Add additional security measures
      format: {
        comments: false, // Remove comments from production build
      },
    },
    rollupOptions: {
      // External packages that should not be bundled
      external: [],
      output: {
        // Ensure proper chunk naming
        chunkFileNames: 'assets/[name]-[hash].js',
        manualChunks: (id) => {
          // Create separate chunks for major dependencies
          if (id.includes('node_modules')) {
            // Core React ecosystem
            if (id.includes('react') || id.includes('react-dom')) return 'vendor-react';
            if (id.includes('react-router')) return 'vendor-router';

            // State management and data fetching
            if (id.includes('@tanstack/react-query')) return 'vendor-query';
            if (id.includes('@supabase')) return 'vendor-supabase';

            // UI libraries
            if (id.includes('@radix-ui') || id.includes('lucide-react')) return 'vendor-ui';
            if (id.includes('framer-motion')) return 'vendor-animation';

            // Charts and visualization
            if (id.includes('recharts') || id.includes('chart.js') || id.includes('react-chartjs-2')) return 'vendor-charts';

            // Utilities
            if (id.includes('date-fns') || id.includes('zod') || id.includes('clsx')) return 'vendor-utils';

            // Large external APIs
            if (id.includes('googleapis') || id.includes('google-auth-library')) return 'vendor-google';
            if (id.includes('cohere-ai')) return 'vendor-ai';

            // File processing
            if (id.includes('exceljs') || id.includes('xlsx') || id.includes('jspdf')) return 'vendor-files';

            return 'vendor-misc'; // All other dependencies
          }

          // Split app code by feature
          if (id.includes('/src/pages/')) {
            if (id.includes('Dashboard')) return 'page-dashboard';
            if (id.includes('Transactions')) return 'page-transactions';
            if (id.includes('budget')) return 'page-budget';
            if (id.includes('Analytics')) return 'page-analytics';
            if (id.includes('Admin')) return 'page-admin';
            return 'pages-misc';
          }

          if (id.includes('/src/components/')) {
            if (id.includes('/admin/')) return 'feature-admin';
            if (id.includes('/auth/')) return 'feature-auth';
            if (id.includes('/budget/')) return 'feature-budget';
            if (id.includes('/charts/')) return 'feature-charts';
            if (id.includes('/credit-card/')) return 'feature-credit-card';
            if (id.includes('/dashboards/')) return 'feature-dashboards';
            if (id.includes('/settings/')) return 'feature-settings';
            if (id.includes('/ai/') || id.includes('AI')) return 'feature-ai';
            return 'components-misc';
          }

          if (id.includes('/src/contexts/')) return 'contexts';
          if (id.includes('/src/services/')) return 'services';
          if (id.includes('/src/utils/')) return 'utils';
          if (id.includes('/src/hooks/')) return 'hooks';
        }
      }
    }
  },
  optimizeDeps: {
    // Force optimization on every restart
    force: true,
    // Include specific dependencies that need optimization
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      '@tanstack/react-query-devtools',
      'exceljs', // Add ExcelJS to optimized dependencies
      'google-auth-library'
    ],
    // Exclude problematic dependencies
    exclude: ['googleapis'],
    esbuildOptions: {
      // Set a single target for dependencies
      target: 'es2020',
      // Define supported features
      supported: {
        bigint: true
      },
      // Add security options for esbuild (addresses CVE-2023-49286)
      define: {
        'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
      },
      // Limit external requests
      allowOverwrite: false,
      platform: 'browser'
    }
  },
  // Clear the cache directory on startup
  cacheDir: '.vite_cache',
})