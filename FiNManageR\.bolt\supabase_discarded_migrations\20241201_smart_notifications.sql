-- Smart Notifications and Automation System
-- Week 4: Advanced Automation & Real-time Integration

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Smart Notifications Table
CREATE TABLE smart_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    category TEXT NOT NULL CHECK (category IN ('payment_reminder', 'utilization_warning', 'optimization_suggestion', 'automation_alert', 'milestone_achievement')),
    priority TEXT NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    trigger_type TEXT NOT NULL CHECK (trigger_type IN ('time_based', 'event_based', 'threshold_based', 'ai_suggested')),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    action_text TEXT,
    action_url TEXT,
    metadata JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT FALSE,
    is_dismissed BOOLEAN DEFAULT FALSE,
    scheduled_for TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Automation Rules Table
CREATE TABLE automation_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    is_enabled BOOLEAN DEFAULT TRUE,
    trigger_config JSONB NOT NULL,
    conditions JSONB NOT NULL,
    actions JSONB NOT NULL,
    safety_checks JSONB DEFAULT '{}',
    execution_log JSONB DEFAULT '{"execution_count": 0, "success_count": 0, "failure_count": 0, "last_executed": null}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notification Rules Table
CREATE TABLE notification_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    conditions JSONB NOT NULL,
    actions JSONB NOT NULL,
    frequency_limits JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notification Preferences Table
CREATE TABLE notification_preferences (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    channels JSONB DEFAULT '{"in_app": true, "email": true, "push": false, "sms": false}',
    categories JSONB DEFAULT '{}',
    quiet_hours JSONB DEFAULT '{"enabled": true, "start_time": "22:00", "end_time": "08:00", "timezone": "UTC"}',
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Automation Execution Log Table
CREATE TABLE automation_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    automation_rule_id UUID NOT NULL REFERENCES automation_rules(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    execution_context JSONB NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'running', 'success', 'failed', 'cancelled')),
    result JSONB DEFAULT '{}',
    error_message TEXT,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Payment Automation Table
CREATE TABLE payment_automations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    credit_card_id UUID NOT NULL REFERENCES credit_cards(id) ON DELETE CASCADE,
    automation_rule_id UUID REFERENCES automation_rules(id) ON DELETE SET NULL,
    payment_type TEXT NOT NULL CHECK (payment_type IN ('minimum', 'fixed_amount', 'percentage', 'full_balance', 'strategy_based')),
    payment_config JSONB NOT NULL,
    schedule_config JSONB NOT NULL,
    safety_limits JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    last_executed TIMESTAMPTZ,
    next_execution TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Behavior Analytics Table
CREATE TABLE user_behavior_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    behavior_type TEXT NOT NULL,
    event_data JSONB NOT NULL,
    context JSONB DEFAULT '{}',
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    session_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ML Prediction Models Table
CREATE TABLE ml_prediction_models (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    model_type TEXT NOT NULL CHECK (model_type IN ('payment_behavior', 'spending_pattern', 'utilization_trend', 'risk_assessment')),
    model_data JSONB NOT NULL,
    accuracy_score DECIMAL(5,4),
    training_data_count INTEGER DEFAULT 0,
    last_trained TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Credit Score Tracking Table
CREATE TABLE credit_score_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    score INTEGER NOT NULL CHECK (score >= 300 AND score <= 850),
    score_source TEXT NOT NULL,
    factors JSONB DEFAULT '{}',
    utilization_impact JSONB DEFAULT '{}',
    predicted_changes JSONB DEFAULT '{}',
    recorded_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notification Delivery Log Table
CREATE TABLE notification_deliveries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID NOT NULL REFERENCES smart_notifications(id) ON DELETE CASCADE,
    channel TEXT NOT NULL CHECK (channel IN ('in_app', 'email', 'push', 'sms')),
    status TEXT NOT NULL CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'bounced')),
    sent_at TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    opened_at TIMESTAMPTZ,
    clicked_at TIMESTAMPTZ,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_smart_notifications_user_id ON smart_notifications(user_id);
CREATE INDEX idx_smart_notifications_category ON smart_notifications(category);
CREATE INDEX idx_smart_notifications_priority ON smart_notifications(priority);
CREATE INDEX idx_smart_notifications_is_read ON smart_notifications(is_read);
CREATE INDEX idx_smart_notifications_created_at ON smart_notifications(created_at);

CREATE INDEX idx_automation_rules_user_id ON automation_rules(user_id);
CREATE INDEX idx_automation_rules_is_enabled ON automation_rules(is_enabled);

CREATE INDEX idx_automation_executions_rule_id ON automation_executions(automation_rule_id);
CREATE INDEX idx_automation_executions_user_id ON automation_executions(user_id);
CREATE INDEX idx_automation_executions_status ON automation_executions(status);

CREATE INDEX idx_payment_automations_user_id ON payment_automations(user_id);
CREATE INDEX idx_payment_automations_card_id ON payment_automations(credit_card_id);
CREATE INDEX idx_payment_automations_next_execution ON payment_automations(next_execution);

CREATE INDEX idx_user_behavior_user_id ON user_behavior_analytics(user_id);
CREATE INDEX idx_user_behavior_type ON user_behavior_analytics(behavior_type);
CREATE INDEX idx_user_behavior_timestamp ON user_behavior_analytics(timestamp);

CREATE INDEX idx_credit_score_user_id ON credit_score_tracking(user_id);
CREATE INDEX idx_credit_score_recorded_at ON credit_score_tracking(recorded_at);

-- Row Level Security (RLS)
ALTER TABLE smart_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_automations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_behavior_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE ml_prediction_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE credit_score_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_deliveries ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own notifications" ON smart_notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own notifications" ON smart_notifications
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON smart_notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own notifications" ON smart_notifications
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their automation rules" ON automation_rules
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their notification rules" ON notification_rules
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their notification preferences" ON notification_preferences
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their automation executions" ON automation_executions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their payment automations" ON payment_automations
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their behavior analytics" ON user_behavior_analytics
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their ML models" ON ml_prediction_models
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their credit score tracking" ON credit_score_tracking
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their notification deliveries" ON notification_deliveries
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM smart_notifications 
            WHERE id = notification_deliveries.notification_id 
            AND user_id = auth.uid()
        )
    );

-- Functions for automation
CREATE OR REPLACE FUNCTION update_automation_execution_log()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the execution log in automation_rules
    UPDATE automation_rules 
    SET execution_log = jsonb_set(
        jsonb_set(
            jsonb_set(
                execution_log,
                '{execution_count}',
                ((execution_log->>'execution_count')::int + 1)::text::jsonb
            ),
            '{last_executed}',
            to_jsonb(NEW.started_at::text)
        ),
        CASE 
            WHEN NEW.status = 'success' THEN '{success_count}'
            WHEN NEW.status = 'failed' THEN '{failure_count}'
            ELSE '{execution_count}'
        END,
        CASE 
            WHEN NEW.status = 'success' THEN ((execution_log->>'success_count')::int + 1)::text::jsonb
            WHEN NEW.status = 'failed' THEN ((execution_log->>'failure_count')::int + 1)::text::jsonb
            ELSE execution_log->'execution_count'
        END
    ),
    updated_at = NOW()
    WHERE id = NEW.automation_rule_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_automation_execution_log
    AFTER INSERT OR UPDATE ON automation_executions
    FOR EACH ROW
    EXECUTE FUNCTION update_automation_execution_log();

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply update timestamp triggers
CREATE TRIGGER update_smart_notifications_updated_at BEFORE UPDATE ON smart_notifications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_automation_rules_updated_at BEFORE UPDATE ON automation_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_notification_rules_updated_at BEFORE UPDATE ON notification_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_notification_preferences_updated_at BEFORE UPDATE ON notification_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_automations_updated_at BEFORE UPDATE ON payment_automations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ml_prediction_models_updated_at BEFORE UPDATE ON ml_prediction_models FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default notification preferences for existing users
INSERT INTO notification_preferences (user_id, channels, categories, quiet_hours)
SELECT 
    id,
    '{"in_app": true, "email": true, "push": false, "sms": false}',
    '{
        "payment_reminders": {"enabled": true, "advance_days": 3, "optimal_timing": true},
        "utilization_warnings": {"enabled": true, "threshold_percentage": 80, "frequency": "immediate"},
        "optimization_suggestions": {"enabled": true, "minimum_savings": 50, "frequency": "weekly"},
        "automation_alerts": {"enabled": true, "include_success": false, "include_failures": true},
        "milestone_achievements": {"enabled": true, "celebration_level": "all"}
    }',
    '{"enabled": true, "start_time": "22:00", "end_time": "08:00", "timezone": "UTC"}'
FROM auth.users
WHERE id NOT IN (SELECT user_id FROM notification_preferences);

-- Comments for documentation
COMMENT ON TABLE smart_notifications IS 'Stores intelligent notifications generated by the smart notification engine';
COMMENT ON TABLE automation_rules IS 'User-defined automation rules for payments and notifications';
COMMENT ON TABLE notification_rules IS 'Rules for generating specific types of notifications';
COMMENT ON TABLE notification_preferences IS 'User preferences for notification delivery and timing';
COMMENT ON TABLE automation_executions IS 'Log of automation rule executions and their results';
COMMENT ON TABLE payment_automations IS 'Automated payment configurations and schedules';
COMMENT ON TABLE user_behavior_analytics IS 'User behavior data for machine learning and personalization';
COMMENT ON TABLE ml_prediction_models IS 'Machine learning models for predictive notifications';
COMMENT ON TABLE credit_score_tracking IS 'Credit score history and impact tracking';
COMMENT ON TABLE notification_deliveries IS 'Log of notification delivery attempts and results';
