{"dashboard": {"id": null, "title": "FiNManageR Phase 7 - Advanced AI Analytics", "tags": ["finmanager", "telegram", "ai", "phase7"], "timezone": "browser", "panels": [{"id": 1, "title": "NLP Confidence Distribution", "type": "histogram", "targets": [{"expr": "histogram_quantile(0.95, rate(finmanager_nlp_confidence_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(finmanager_nlp_confidence_bucket[5m]))", "legendFormat": "Median"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "OCR Processing Performance", "type": "graph", "targets": [{"expr": "rate(finmanager_ocr_processing_time_seconds_sum[5m]) / rate(finmanager_ocr_processing_time_seconds_count[5m])", "legendFormat": "Average Processing Time"}, {"expr": "rate(finmanager_ocr_confidence_sum[5m]) / rate(finmanager_ocr_confidence_count[5m])", "legendFormat": "Average Confidence"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "User Interaction Patterns", "type": "table", "targets": [{"expr": "sum by (interaction_type) (rate(finmanager_user_interaction_total[1h]))", "format": "table"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Family Group Activity", "type": "stat", "targets": [{"expr": "finmanager_active_users{period=\"24h\"}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Error Rate Trends", "type": "graph", "targets": [{"expr": "rate(finmanager_error_rate[5m])", "legendFormat": "Error Rate"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}}, {"id": 6, "title": "Response Time P95", "type": "singlestat", "targets": [{"expr": "finmanager_response_time_p95_seconds"}], "gridPos": {"h": 4, "w": 6, "x": 0, "y": 24}}, {"id": 7, "title": "Active Users (24h)", "type": "singlestat", "targets": [{"expr": "finmanager_active_users{period=\"24h\"}"}], "gridPos": {"h": 4, "w": 6, "x": 6, "y": 24}}, {"id": 8, "title": "NLP Fallback Rate", "type": "singlestat", "targets": [{"expr": "rate(finmanager_nlp_fallback_total{used=\"true\"}[1h]) / rate(finmanager_nlp_fallback_total[1h]) * 100"}], "gridPos": {"h": 4, "w": 6, "x": 12, "y": 24}}, {"id": 9, "title": "Command Success Rate", "type": "singlestat", "targets": [{"expr": "avg(finmanager_command_success_rate)"}], "gridPos": {"h": 4, "w": 6, "x": 18, "y": 24}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}}