-- Add metadata column to transactions table if it doesn't exist
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb;

-- Create index on metadata for better query performance
CREATE INDEX IF NOT EXISTS idx_transactions_metadata ON transactions USING GIN (metadata);

-- Update comment on column
COMMENT ON COLUMN transactions.metadata IS 'Additional transaction metadata like payment method, tags, etc.';

-- Verify recurring_transactions table structure
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'recurring_transactions' AND column_name = 'transaction_template'
  ) THEN
    -- Add transaction_template column if missing
    ALTER TABLE recurring_transactions ADD COLUMN transaction_template jsonb NOT NULL DEFAULT '{}'::jsonb;
  END IF;
END $$;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
